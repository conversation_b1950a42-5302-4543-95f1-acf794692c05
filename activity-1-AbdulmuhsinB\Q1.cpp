#include <iostream>
#include <fstream>
#include <string>
#include <algorithm>
#include <vector>
#include

std::vector<std::string> loadList(const std::string& filename) {
    std::vector<std::string> list;
    std::ifstream file(filename);
    std::string line;

    while (std::getline(file, line)) {
        if (!line.empty()) {
            list.push_back(line);
        }
    }

    return list;
}

int main(int argc, char* argv[]) {

    std::vector<std::string> allowList = loadList("./_allow list_.csv");
    std::vector<std::string> restrictList = loadList("./_restrict list_.csv");
    std::vector<std::string> badWordList = loadList("./_bad word list_.csv");

    std::cout << "Number of Files to read: " << argc-1 << std::endl;

    if (argc <= 1) {
        std::cerr << "No Files passed in as Arguments\n";
        return 1;
    }

    for (int i = 1; i < argc; i++) {

        int badWords = 0;
        
        std::ifstream file(argv[i]);
        if (!file.is_open()) {
            std::cerr << "Error::: Could not open the file \"" << argv[i] << '\n';
            return 1;
        }

        std::cout << "File Name: " << argv[i] << std::endl;

        std::string line;
        while (std::getline(file, line)) {

            //check allowed
            bool allowed = false;
            for (auto allowedWord : allowList) {
                if (line.find(allowedWord) != std::string::npos) {
                    allowed = true;
                    break;
                }
            }
            if (allowed) {
                std::cout << "Move this email to inbox: " << line << '\n';
                break;
            }

            //check restricted
            bool restricted = false;
            for (auto restrictedWord : restrictList) {
                if (line.find(restrictedWord) != std::string::npos) {
                    restricted = true;
                    break;
                }
            }
            if (restricted) {
                std::cout << "Move this email to spam: " << line << '\n';
                break;
            }

            //check bad words
            //bool badWords = false;
            // int badWords = 0;
            for (auto badWord : badWordList) {
                if (line.find(badWord) != std::string::npos) {
                    //badWords = true;
                    badWords++;
                }
            }
            // if (badWords > 5) {
            //     std::cout << "Badwords found go to spam: " << line << '\n';
            //     break;
            // }

            
        }

        if (badWords > 5) {
                std::cout << "Badwords found go to spam: " << '\n';
            }

        file.close();

    }
    
    return 0;
}
